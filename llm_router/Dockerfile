FROM litellm/litellm:v1.73.0-stable

# Install runtime dependencies
RUN apk add --no-cache openssl postgresql-client

WORKDIR /app

# Copy base LiteLLM files (llm_router/ excluded via .dockerignore)
COPY llm_router/ /app/llm_router/

# Install HTTP client for predictor service and testing dependencies
RUN pip install requests pytest pytest-asyncio tabulate

# Install analytics dependencies for database and health monitoring
RUN pip install asyncpg psycopg2-binary pandas

# Append analytics schema to main schema.prisma to create complete schema (skip datasource and generator)
RUN tail -n +9 /app/llm_router/analytics/schema.prisma >> /app/schema.prisma
RUN tail -n +9 /app/llm_router/analytics/schema.prisma >> /app/litellm/proxy/schema.prisma
RUN tail -n +9 /app/llm_router/analytics/schema.prisma >> /app/litellm-proxy-extras/litellm_proxy_extras/schema.prisma
RUN tail -n +9 /app/llm_router/analytics/schema.prisma >> /usr/lib/python3.13/site-packages/litellm/proxy/schema.prisma
RUN tail -n +9 /app/llm_router/analytics/schema.prisma >> /usr/lib/python3.13/site-packages/litellm_proxy_extras/schema.prisma

# Add analytics schema to main LiteLLM proxy schema

# Copy analytics migrations to both LiteLLM migrations directories and to prisma default location
COPY llm_router/analytics/migrations/ /app/litellm-proxy-extras/litellm_proxy_extras/migrations/
COPY llm_router/analytics/migrations/ /usr/lib/python3.13/site-packages/litellm_proxy_extras/migrations/

# Create prisma directory and copy migrations there (prisma looks for migrations in prisma/migrations by default)
RUN mkdir -p /app/prisma
COPY llm_router/analytics/migrations/ /app/prisma/migrations/

# Generate prisma clients for merged schema
RUN prisma generate

# Copy license.py to specific locations for LiteLLM proxy auth
COPY llm_router/license.py /usr/local/lib/python3.13/site-packages/litellm/proxy/auth/litellm_license.py
COPY llm_router/license.py /usr/lib/python3.13/site-packages/litellm/proxy/auth/litellm_license.py
COPY llm_router/license.py /app/litellm/proxy/auth/litellm_license.py

# Set permissions for llm_router entrypoint
RUN chmod +x /app/llm_router/entrypoint.sh

# Set environment variable for config
#ENV LITELLM_CONFIG_PATH="/app/llm_router/litellm.config.yaml"
#ENV CONFIG_FILE_PATH="/app/llm_router/litellm.config.yaml"

# Set entrypoint and command
ENTRYPOINT ["/app/llm_router/entrypoint.sh"]
CMD ["python", "/app/llm_router/start_litellm_with_router.py", "--port", "4000"]
